package org.easitline.common.db;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.EasyPool;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.log.JDBCLogger;

import com.alibaba.druid.pool.DruidDataSource;

public class ConnectionMetaData {

	  private String user;

	  private String password;

	  private String url;

	  private String appDatasourceName;
	  
	  private String appName;
	  
	  private String sysDatasourceName ;
	  
	  /**
	   * JDBC 驱动：1; datasource:2 ; sys:3
	   */
	  private int   factoryType =1 ;

	  // 添加静态缓存
	  private static final ConcurrentHashMap<String, DBTypes> DB_TYPE_CACHE = new ConcurrentHashMap<>();
	  
	  // 添加实例变量缓存数据库类型
	  private volatile DBTypes dbType;

	  public ConnectionMetaData(String sysDatasourceName) {
		this.factoryType = 3;
		this.sysDatasourceName = sysDatasourceName;
	  }
	  
	  public ConnectionMetaData( String appName,String appDatasourceName) {
		this.factoryType = 2;
		this.appDatasourceName = appDatasourceName;
		this.appName = appName;
	  }
	  
	  public ConnectionMetaData( String url,String user, String password) {
		this.factoryType = 1;
		this.user = user;
		this.password = password;
		this.url = url;
	 }
	  
	
	public String getAppName() {
		return appName;
	}
	
	public String getSysDatasourceName() {
		return sysDatasourceName;
	}
	
	public String getAppDatasourceName() {
		return appDatasourceName;
	}
	
	/**
	 * 获得数据库连接
	 * @return
	 * @throws SQLException
	 */
	public Connection getConnection() throws SQLException{
		 Connection conn = null;
		 if (this.factoryType == 1) {
			 conn =  java.sql.DriverManager.getConnection(this.url,this.user,this.password);
		 }else {
			 conn = getDruidDataSource().getConnection();
		 }
		 // 移除强制设置autoCommit=true，让事务管理器控制事务状态
		 return conn;
	}
	
	public DruidDataSource getDruidDataSource() throws SQLException {
		if (this.factoryType == 2) {
			 AppContext appContext = AppContext.getContext(this.appName);
			 String sysDsName  =  appContext.getDatasourceName(this.appDatasourceName);
			 this.sysDatasourceName = sysDsName;
			 return EasyPool.getInstance().getDruidDatasource(sysDsName);
		}
		 if(this.factoryType == 3){ 
			 return EasyPool.getInstance().getDruidDatasource(sysDatasourceName);
		 }
		return null;
	}
	
	
	public DBTypes getDriverType() {
		// 第一次检查 - 无锁快速路径
		if (dbType != null) {
			return dbType;
		}

		// 双重检查锁定
		synchronized (this) {
			// 第二次检查 - 防止竞态条件
			if (dbType != null) {
				return dbType;
			}

			// 生成缓存key
			String cacheKey = getCacheKey();
			if (cacheKey != null) {
				DBTypes cachedType = DB_TYPE_CACHE.get(cacheKey);
				if (cachedType != null) {
					dbType = cachedType;  // 缓存到实例变量
					return cachedType;
				}
			}

			// 缓存未命中,需要连接数据库判断类型
			Connection conn = null;
			try {
				conn = this.getConnection();
				String connUrl = conn.getMetaData().getURL().toLowerCase();

				dbType = determineDBType(connUrl);

				// 如果有缓存key,加入缓存
				if (cacheKey != null) {
					DB_TYPE_CACHE.put(cacheKey, dbType);
				}

				return dbType;

			} catch (Exception ex) {
				JDBCLogger.getLogger().error("getDriverType() exception,cause:" + ex.getMessage(), ex);
				return DBTypes.OTHER;
			} finally {
				closeQuietly(conn);
			}
		}
	}

	// 添加缓存key生成方法
	private String getCacheKey() {
		if (factoryType == 3 && sysDatasourceName != null) {
			return "sys:" + sysDatasourceName;
		}
		if (factoryType == 2 && appName != null && appDatasourceName != null) {
			try {
				AppContext appContext = AppContext.getContext(this.appName);
				String sysDsName = appContext.getDatasourceName(this.appDatasourceName);
				return "app:" + sysDsName;
			} catch (Exception e) {
				JDBCLogger.getLogger().error("Failed to get system datasource name", e);
			}
		}
		return null;
	}

	// 添加缓存清理方法
	public static void clearDBTypeCache() {
		DB_TYPE_CACHE.clear();
	}

	public static void removeDBTypeCache(String appName, String appDatasourceName) {
		try {
			AppContext appContext = AppContext.getContext(appName);
			String sysDsName = appContext.getDatasourceName(appDatasourceName);
			DB_TYPE_CACHE.remove("app:" + sysDsName);
		} catch (Exception e) {
			JDBCLogger.getLogger().error("Failed to remove cache", e);
		}
	}

	public static void removeSysDBTypeCache(String sysDatasourceName) {
		DB_TYPE_CACHE.remove("sys:" + sysDatasourceName);
	}

	// 优化数据库类型判断逻辑
	private DBTypes determineDBType(String url) {
		if (url.contains("mysql")) return DBTypes.MYSQL;
		if (url.contains("oracle")) return DBTypes.ORACLE; 
		if (url.contains("postgresql")) return DBTypes.PostgreSql;
		if (url.contains("sybase")) return DBTypes.SYBASE;
		if (url.contains("db2")) return DBTypes.DB2;
		if (url.contains("sqlserver") || url.contains("sqljdbc")) return DBTypes.SQLSERVER;
		if (url.contains("sqlite")) return DBTypes.SQLITE;
		if (url.contains("opengauss")) return DBTypes.OPENGAUSS;
		if (url.contains("jdbc:dm")) return DBTypes.DAMENG;
		return DBTypes.OTHER;
	}

	// 安全关闭连接
	private void closeQuietly(Connection conn) {
		if (conn != null) {
			try {
				conn.close();
			} catch (Exception e) {
				JDBCLogger.getLogger().error("Failed to closeQuietly", e);
			}
		}
	}
}
