package org.easitline.common.db.helper;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import org.apache.log4j.Logger;
import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.log.JDBCLogger;

/**
 * 数据库连接和事务管理工具类
 * 提供统一的连接管理、事务控制和资源释放功能
 */
public class ConnectionHelper {

	/**
	 * 开始数据库事务
	 * 获取数据库连接并关闭自动提交
	 *
	 * @param metaData 连接元数据
	 * @param transactionConnection 缓存的连接
	 * @return 事务连接
	 * @throws SQLException 数据库异常
	 */
	public static Connection beginTransaction(ConnectionMetaData metaData, Connection transactionConnection) throws SQLException {
		if (transactionConnection != null) {
			// 如果缓存不为空，则采用当前的连接，防止多次执行begin的操作
			return transactionConnection;
		}

		if (metaData == null) {
			throw new SQLException("数据库连接元数据未初始化，无法开始事务");
		}

		Connection connection = metaData.getConnection();
		if (connection != null && !connection.isClosed()) {
			connection.setAutoCommit(false);
		}
		return connection;
	}

	/**
	 * 提交数据库事务
	 * 提交所有更改并恢复自动提交模式
	 *
	 * @param connection 数据库连接
	 * @throws SQLException 数据库异常
	 */
	public static void commitTransaction(Connection connection) throws SQLException {
		if (connection == null) {
			throw new SQLException("没有活动的事务可以提交");
		}

		connection.commit();
		connection.setAutoCommit(true);
	}

	/**
	 * 回滚数据库事务
	 * 回滚所有未提交的更改并恢复自动提交模式
	 *
	 * @param connection 数据库连接
	 * @throws SQLException 数据库异常
	 */
	public static void rollbackTransaction(Connection connection) throws SQLException {
		if (connection == null) {
			throw new SQLException("没有活动的事务可以回滚");
		}

		connection.rollback();
		connection.setAutoCommit(true);
	}

	/**
	 * 获取数据库连接
	 * 如果在事务中则返回缓存的连接,否则创建新连接
	 *
	 * @param metaData 连接元数据
	 * @param transactionConnection 缓存的连接
	 * @return 数据库连接
	 * @throws SQLException 数据库异常
	 */
	public static Connection getConnection(ConnectionMetaData metaData, Connection transactionConnection) throws SQLException {
		if (transactionConnection != null) {
			return transactionConnection;
		}

		if (metaData == null) {
			throw new SQLException("ConnectionMetaData not initialized and no cached connection available");
		}

		return metaData.getConnection();
	}

	/**
	 * 关闭数据库连接和相关资源
	 *
	 * @param connection 数据库连接
	 * @param logger 日志记录器
	 */
	public static void closeConnection(Connection connection, Logger logger) {
		try {
			if (connection != null) {
				connection.close();
			}
		} catch (Exception e) {
			if (logger != null) {
				logger.error(e, e);
			} else {
				JDBCLogger.getLogger().error(e, e);
			}
		}
	}

	/**
	 * 释放数据库资源
	 *
	 * @param rs ResultSet
	 * @param stmt Statement
	 * @param conn Connection
	 * @param transactionConnection 缓存的连接（如果存在事务则不关闭连接）
	 * @param logger 日志记录器
	 */
	public static void closeResources(ResultSet rs, Statement stmt, Connection conn,
			Connection transactionConnection, Logger logger) {
		try {
			if (rs != null) {
				rs.close();
			}
		} catch (Exception ex) {
			if (logger != null) {
				logger.error(ex, ex);
			} else {
				JDBCLogger.getLogger().error(ex, ex);
			}
		}

		try {
			if (stmt != null) {
				stmt.close();
			}
		} catch (Exception ex) {
			if (logger != null) {
				logger.error(ex, ex);
			} else {
				JDBCLogger.getLogger().error(ex, ex);
			}
		}

		// 如果存在缓存连接（事务中），则不关闭连接
		if (transactionConnection != null) {
			return;
		}

		try {
			if (conn != null) {
				conn.close();
			}
		} catch (Exception ex) {
			if (logger != null) {
				logger.error(ex, ex);
			} else {
				JDBCLogger.getLogger().error(ex, ex);
			}
		}
	}
}
